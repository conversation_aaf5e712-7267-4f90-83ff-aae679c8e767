#!/usr/bin/env python3
"""
Enhanced Keno Predictor - K<PERSON>t hợp LSTM Model + Phân tích ngắn hạn 7 kì
Core functions:
- Phân tích xu hướng ngắn hạn 7 kì
- Tạo vector đặc trưng
- Kết hợp LSTM model và phân tích xu hướng
- Test với dữ liệu từ 2025-04-01, dự đoán từ kì 51
"""

import numpy as np
import time
from datetime import datetime
from variable_length_model import VariableLengthKenoModel, connect_db

class EnhancedKenoPredictor:
    """
    Enhanced Predictor với core functions:
    - Phân tích xu hướng ngắn hạn 7 kì
    - Tạo vector đặc trưng
    - Kết hợp LSTM model + phân tích xu hướng
    - Test với dữ liệu từ 2025-04-01
    """

    def __init__(self, enable_short_term=False, enable_exclusion=False, enable_ensemble=False):
        self.short_term_period = 10  # Đổi từ 7 kì lên 10 kì
        self.data = None
        self.lstm_model = None
        self.model_path = "keno_variable_length_model.h5"
        self.excluded_numbers_tracker = {}  # Theo dõi số bị loại bỏ {number: last_appearance_index}
        self.current_test_date = None  # Theo dõi ngày hiện tại để clear khi đổi ngày
        self.daily_results = []  # Lưu kết quả từng ngày để thống kê
        self.labeled_data = []  # Lưu dữ liệu có nhãn để train model

        # Feature flags
        self.enable_short_term = enable_short_term  # Bật/tắt phân tích ngắn hạn 10 kì
        self.enable_exclusion = enable_exclusion    # Bật/tắt loại bỏ số nóng
        self.enable_ensemble = enable_ensemble      # Bật/tắt ensemble prediction

        self.load_lstm_model()

    def load_lstm_model(self):
        """Load LSTM model"""
        try:
            import tensorflow as tf
            self.lstm_model = VariableLengthKenoModel()
            self.lstm_model.model = tf.keras.models.load_model(self.model_path)
        except Exception:
            self.lstm_model = None

    def load_recent_data(self, limit_draws=100):
        """Load dữ liệu gần nhất"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT date, time, results, period
                FROM histories_keno
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (limit_draws,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) < self.short_term_period:
                return False

            # Chuyển đổi dữ liệu và reverse để có thứ tự từ cũ đến mới
            self.data = []
            for row in reversed(rows):
                numbers = [int(n) for n in row['results'].split(',')]
                self.data.append({
                    'date': row['date'],
                    'time': row['time'],
                    'numbers': numbers,
                    'period': row['period']
                })

            return True

        except Exception:
            return False

    def create_frequency_vector(self, data_period):
        """Tạo vector tần suất xuất hiện"""
        frequency_vector = np.zeros(80)
        total_draws = len(data_period)

        if total_draws == 0:
            return frequency_vector

        for draw in data_period:
            for num in draw['numbers']:
                if 1 <= num <= 80:
                    frequency_vector[num-1] += 1

        # Chuyển thành tỷ lệ phần trăm
        frequency_vector = (frequency_vector / total_draws) * 100
        return frequency_vector

    def create_missing_vector(self, data_period):
        """Tạo vector số lần bị trượt"""
        missing_vector = np.zeros(80)
        total_draws = len(data_period)

        if total_draws == 0:
            return missing_vector

        for draw in data_period:
            appeared_numbers = set(draw['numbers'])
            for num in range(1, 81):
                if num not in appeared_numbers:
                    missing_vector[num-1] += 1

        # Chuyển thành tỷ lệ phần trăm
        missing_vector = (missing_vector / total_draws) * 100
        return missing_vector

    def clear_excluded_numbers_for_new_day(self, test_date):
        """Clear danh sách số loại bỏ khi chạy ngày mới"""
        if self.current_test_date != test_date:
            self.excluded_numbers_tracker = {}
            self.current_test_date = test_date
            print(f"🔄 Clear danh sách số loại bỏ cho ngày mới: {test_date}")

    def update_excluded_numbers_tracker(self, data_period, current_draw_index):
        """
        Cập nhật tracker số bị loại bỏ:
        - Thêm số xuất hiện quá 3 lần trong 5 kì gần nhất
        - Xóa số không xuất hiện trong 5 kì liên tiếp
        """
        if len(data_period) < 5:
            return set()

        # Lấy 5 kì gần nhất
        last_5_draws = data_period[-5:]

        # 1. Thêm số xuất hiện quá 3 lần trong 5 kì vào tracker
        for num in range(1, 81):
            appearance_count = 0

            # Đếm số lần xuất hiện trong 5 kì gần nhất
            for draw in last_5_draws:
                if num in draw['numbers']:
                    appearance_count += 1

            # Nếu xuất hiện quá 3 lần trong 5 kì, thêm vào tracker
            if appearance_count > 3:
                self.excluded_numbers_tracker[num] = current_draw_index

        # 2. Xóa số không xuất hiện trong 5 kì liên tiếp khỏi tracker (đưa lại vào dự đoán)
        numbers_to_remove = []
        for num, last_excluded_index in self.excluded_numbers_tracker.items():
            # Kiểm tra 5 kì gần nhất kể từ khi bị loại bỏ
            gap_since_excluded = current_draw_index - last_excluded_index

            if gap_since_excluded >= 5:
                # Kiểm tra 5 kì liên tiếp gần nhất (từ khi bị loại bỏ)
                start_check_index = max(0, len(data_period) - gap_since_excluded)
                check_draws = data_period[start_check_index:]

                # Đếm số kì liên tiếp không xuất hiện từ cuối lên
                consecutive_missing = 0
                for draw in reversed(check_draws):
                    if num not in draw['numbers']:
                        consecutive_missing += 1
                    else:
                        break

                # Nếu không xuất hiện trong 5 kì liên tiếp → Đưa lại vào dự đoán
                if consecutive_missing >= 5:
                    numbers_to_remove.append(num)

        # Xóa các số đã "hết hạn" khỏi tracker (cho phép dự đoán lại)
        for num in numbers_to_remove:
            del self.excluded_numbers_tracker[num]

        return set(self.excluded_numbers_tracker.keys())

    def analyze_consecutive_appearances(self, data_period, current_draw_index=None):
        """
        Phân tích số xuất hiện quá thường xuyên và quản lý danh sách loại bỏ
        Logic: Loại bỏ số xuất hiện quá 3 lần trong 5 kì gần nhất
        """
        if current_draw_index is None:
            current_draw_index = len(data_period)

        return self.update_excluded_numbers_tracker(data_period, current_draw_index)

    def create_prediction_label(self, correct_count):
        """
        Tạo nhãn dựa trên số dự đoán đúng:
        - 6/6: Very Good
        - 5/6: Good
        - 4/6: Normal
        - 3/6 trở xuống: Bad
        """
        if correct_count >= 6:
            return "Very Good"
        elif correct_count >= 5:
            return "Good"
        elif correct_count >= 4:
            return "Normal"
        else:
            return "Bad"

    def save_labeled_data_entry(self, day_results, predicted_missing, actual_results, correct_count, draw_info):
        """Lưu một entry dữ liệu có nhãn"""
        if not self.enable_short_term:
            return  # Chỉ lưu khi có tính năng ngắn hạn

        label = self.create_prediction_label(correct_count)

        # Tạo feature vector từ dữ liệu hiện tại
        feature_data = self.create_feature_vector()
        short_trend = self.analyze_short_term_trend()

        if feature_data and short_trend:
            entry = {
                'date': draw_info['date'],
                'draw_number': draw_info['draw_number'],
                'time': draw_info['time'],
                'day_results_count': len(day_results),
                'predicted_missing': predicted_missing,
                'actual_results': actual_results,
                'correct_count': correct_count,
                'label': label,
                'features': {
                    'short_term_period': self.short_term_period,
                    'excluded_numbers': list(short_trend.get('excluded_numbers', set())),
                    'number_stats': short_trend['number_stats'],
                    'feature_vectors': {k: v.tolist() for k, v in feature_data['vectors'].items()}
                },
                'enable_exclusion': self.enable_exclusion,
                'enable_ensemble': self.enable_ensemble
            }

            self.labeled_data.append(entry)

    def save_labeled_data_to_file(self, filename="keno_labeled_data.json"):
        """Lưu dữ liệu có nhãn ra file JSON"""
        import json
        from datetime import datetime

        if not self.labeled_data:
            print("Không có dữ liệu có nhãn để lưu")
            return

        # Thống kê nhãn
        label_counts = {}
        for entry in self.labeled_data:
            label = entry['label']
            label_counts[label] = label_counts.get(label, 0) + 1

        # Tạo metadata
        metadata = {
            'created_at': datetime.now().isoformat(),
            'total_entries': len(self.labeled_data),
            'label_distribution': label_counts,
            'features_enabled': {
                'short_term': self.enable_short_term,
                'exclusion': self.enable_exclusion,
                'ensemble': self.enable_ensemble
            },
            'short_term_period': self.short_term_period
        }

        # Cấu trúc file
        output_data = {
            'metadata': metadata,
            'data': self.labeled_data
        }

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            print(f"\n📁 Đã lưu {len(self.labeled_data)} entries vào {filename}")
            print(f"📊 Phân bố nhãn:")
            for label, count in label_counts.items():
                percentage = (count / len(self.labeled_data)) * 100
                print(f"   • {label}: {count} entries ({percentage:.1f}%)")

        except Exception as e:
            print(f"Lỗi khi lưu file: {e}")

    def analyze_short_term_trend(self, current_draw_index=None):
        """Phân tích xu hướng ngắn hạn 10 kì"""
        if len(self.data) < self.short_term_period:
            return None

        # Lấy 10 kì gần nhất
        recent_data = self.data[-self.short_term_period:]

        # Tạo vector đặc trưng
        frequency_vector = self.create_frequency_vector(recent_data)
        missing_vector = self.create_missing_vector(recent_data)

        # Phân tích số xuất hiện liên tục cần loại bỏ
        excluded_numbers = self.analyze_consecutive_appearances(recent_data, current_draw_index)

        # Phân tích pattern xuất hiện
        number_stats = {}
        for num in range(1, 81):
            appearances = []
            for i, draw in enumerate(recent_data):
                if num in draw['numbers']:
                    appearances.append(i)

            # Tính các chỉ số
            freq_rate = frequency_vector[num-1]
            miss_rate = missing_vector[num-1]
            last_appearance = max(appearances) if appearances else -1
            gap_from_last = len(recent_data) - 1 - last_appearance if last_appearance >= 0 else len(recent_data)

            number_stats[num] = {
                'frequency_rate': freq_rate,
                'missing_rate': miss_rate,
                'appearances': len(appearances),
                'last_appearance': last_appearance,
                'gap_from_last': gap_from_last,
                'momentum': freq_rate / 10 * 100,  # Momentum trong 10 kì
                'is_excluded': num in excluded_numbers  # Đánh dấu số bị loại bỏ
            }

        return {
            'period': self.short_term_period,
            'frequency_vector': frequency_vector,
            'missing_vector': missing_vector,
            'number_stats': number_stats,
            'excluded_numbers': excluded_numbers,
            'data': recent_data
        }

    def create_feature_vector(self):
        """Tạo vector đặc trưng cho 10 kì gần nhất"""
        short_trend = self.analyze_short_term_trend()
        if not short_trend:
            return None

        # Tạo vector đặc trưng 7 chiều cho mỗi số
        feature_vectors = {}

        for num in range(1, 81):
            stats = short_trend['number_stats'][num]

            # Vector 7 chiều cho mỗi số
            features = [
                stats['frequency_rate'],     # 0: Tần suất xuất hiện
                stats['missing_rate'],       # 1: Tỷ lệ trượt
                stats['gap_from_last'],      # 2: Khoảng cách từ lần cuối
                stats['momentum'],           # 3: Momentum
                stats['appearances'],        # 4: Số lần xuất hiện
                len(short_trend['data']),    # 5: Tổng số kì phân tích
                stats['last_appearance']     # 6: Vị trí lần xuất hiện cuối
            ]

            feature_vectors[num] = np.array(features)

        return {
            'vectors': feature_vectors,
            'short_trend': short_trend,
            'feature_names': [
                'frequency_rate', 'missing_rate', 'gap_from_last', 'momentum',
                'appearances', 'total_draws', 'last_appearance'
            ]
        }

    def predict_lstm_only(self, day_results, num_predictions=6):
        """Dự đoán chỉ bằng LSTM model (mặc định)"""
        if not self.lstm_model or not day_results or len(day_results) < 50:
            return []

        try:
            # Dự đoán bằng LSTM
            probabilities = self.lstm_model.predict_next_draw(day_results)

            if probabilities is None:
                return []

            # Chuyển đổi probabilities thành missing numbers
            # Số có xác suất thấp = có khả năng trượt cao
            number_probs = [(i+1, prob) for i, prob in enumerate(probabilities)]
            number_probs.sort(key=lambda x: x[1])  # Sắp xếp theo xác suất tăng dần

            predictions = [num for num, _ in number_probs[:num_predictions]]
            return predictions

        except Exception as e:
            return []

    def predict_with_features(self, day_results=None, num_predictions=6, current_draw_index=None):
        """Dự đoán với các tính năng bổ sung (khi enable_ensemble=True)"""
        # Load dữ liệu nếu chưa có
        if not self.data:
            if not self.load_recent_data(100):
                return []

        # 1. Dự đoán LSTM (luôn có)
        lstm_predictions = []
        if self.lstm_model and day_results and len(day_results) >= 50:
            lstm_predictions = self.predict_lstm_only(day_results, num_predictions * 2)

        # 2. Phân tích xu hướng ngắn hạn (nếu enable_short_term=True)
        short_trend = None
        if self.enable_short_term:
            short_trend = self.analyze_short_term_trend(current_draw_index)

        # 3. Tạo vector đặc trưng (nếu enable_short_term=True)
        feature_data = None
        if self.enable_short_term and short_trend:
            feature_data = self.create_feature_vector()

        # 4. Ensemble scoring - kết hợp LSTM + các tính năng bổ sung
        final_scores = {}

        # Trọng số động dựa trên tính năng được bật
        if self.enable_short_term and short_trend:
            lstm_weight = 0.6
            trend_weight = 0.4
        else:
            lstm_weight = 1.0  # Chỉ dùng LSTM
            trend_weight = 0.0

        # Lấy danh sách số bị loại bỏ (nếu enable_exclusion=True)
        excluded_numbers = set()
        if self.enable_exclusion and short_trend:
            excluded_numbers = short_trend.get('excluded_numbers', set())

        for num in range(1, 81):
            # Bỏ qua số bị loại bỏ (nếu enable_exclusion=True)
            if self.enable_exclusion and num in excluded_numbers:
                final_scores[num] = -1000  # Điểm rất thấp để loại bỏ
                continue

            total_score = 0

            # LSTM score (luôn có)
            if lstm_predictions and num in lstm_predictions:
                lstm_score = 100 - lstm_predictions.index(num) * 5
                total_score += lstm_score * lstm_weight

            # Trend analysis score (nếu enable_short_term=True)
            if self.enable_short_term and short_trend:
                stats = short_trend['number_stats'][num]
                missing_score = stats['missing_rate']
                gap_score = min(stats['gap_from_last'] * 10, 100)
                momentum_score = max(0, 100 - stats['momentum'])

                trend_score = (missing_score * 0.4 + gap_score * 0.4 + momentum_score * 0.2)
                total_score += trend_score * trend_weight

                # Bonus từ vector đặc trưng
                if feature_data and num in feature_data['vectors']:
                    features = feature_data['vectors'][num]
                    feature_bonus = features[1] * 0.1  # missing_rate bonus
                    total_score += feature_bonus

            final_scores[num] = total_score

        # Sắp xếp và lấy top predictions (loại bỏ số có điểm âm)
        valid_predictions = [(num, score) for num, score in final_scores.items() if score > 0]
        sorted_predictions = sorted(valid_predictions, key=lambda x: x[1], reverse=True)
        final_predictions = [num for num, _ in sorted_predictions[:num_predictions]]

        return final_predictions

    def ensemble_prediction(self, day_results=None, num_predictions=6, current_draw_index=None):
        """
        Core function: Kết hợp LSTM model + các tính năng bổ sung (deprecated - sử dụng predict_missing_numbers)
        """
        return self.predict_missing_numbers(day_results, num_predictions, current_draw_index)

    def predict_missing_numbers(self, day_results=None, num_predictions=6, current_draw_index=None):
        """
        Main prediction function:
        - Mặc định: Chỉ dùng LSTM model
        - enable_ensemble=True: Kết hợp với các tính năng bổ sung
        """
        # Nếu enable_ensemble=True, sử dụng tính năng bổ sung
        if self.enable_ensemble:
            return self.predict_with_features(day_results, num_predictions, current_draw_index)

        # Mặc định: Chỉ dùng LSTM model
        return self.predict_lstm_only(day_results, num_predictions)

    def get_test_dates(self):
        """Lấy danh sách ngày cần test (từ 2025-04-01)"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT DISTINCT date
                FROM histories_keno
                WHERE date >= '2025-04-01'
                ORDER BY date ASC
            """

            cursor.execute(query)
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            return [row['date'] for row in rows]

        except Exception as e:
            return []

    def get_day_draws(self, test_date):
        """Lấy tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (test_date,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]

            return rows

        except Exception as e:
            return []

    def calculate_accuracy(self, predicted_missing, actual_results):
        """Tính độ chính xác dự đoán số trượt"""
        if not predicted_missing or not actual_results:
            return 0, 0, 0, [], []

        # Số trượt thực tế = các số từ 1-80 không có trong actual_results
        actual_missing = set(range(1, 81)) - set(actual_results)
        predicted_set = set(predicted_missing)

        # Số dự đoán đúng và sai
        correct_numbers = list(predicted_set & actual_missing)  # Số dự đoán đúng
        wrong_numbers = list(predicted_set - actual_missing)    # Số dự đoán sai

        # Số dự đoán đúng
        correct_predictions = len(correct_numbers)

        # Độ chính xác = số dự đoán đúng / tổng số dự đoán
        accuracy = (correct_predictions / len(predicted_missing)) * 100

        return accuracy, correct_predictions, len(predicted_missing), correct_numbers, wrong_numbers

    def calculate_ticket_results(self, predicted_missing, actual_results):
        """
        Tính toán kết quả vé đánh dựa trên 6 số dự đoán
        Với 6 số có thể tạo 15 vé (C(6,4) = 15 vé 4 số)
        """
        if not predicted_missing or len(predicted_missing) != 6:
            return 0, 0, 0.0

        from itertools import combinations

        # Tạo 15 vé từ 6 số dự đoán (mỗi vé 4 số)
        tickets = list(combinations(predicted_missing, 4))
        total_tickets = len(tickets)  # Luôn = 15

        # Số trượt thực tế
        actual_missing = set(range(1, 81)) - set(actual_results)

        # Đếm số vé thắng (4 số đều trượt)
        winning_tickets = 0
        for ticket in tickets:
            if all(num in actual_missing for num in ticket):
                winning_tickets += 1

        # Tỷ lệ thắng
        win_rate = (winning_tickets / total_tickets) * 100 if total_tickets > 0 else 0

        return winning_tickets, total_tickets, win_rate

    def test_single_day(self, test_date):
        """Test dự đoán cho một ngày từ kì 51"""
        print(f"\n=== Test ngày {test_date} ===")

        # Clear danh sách số loại bỏ khi chạy ngày mới
        self.clear_excluded_numbers_for_new_day(test_date)

        # Lấy tất cả kì của ngày
        day_draws = self.get_day_draws(test_date)

        if len(day_draws) < 51:
            print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, bỏ qua")
            return None

        predictions_count = 0
        perfect_predictions = 0  # Số lần dự đoán đúng ≥5/6 số trượt
        excellent_predictions = 0  # Số lần dự đoán đúng 6/6 số trượt
        good_predictions = 0  # Số lần dự đoán đúng 5/6 số trượt
        normal_predictions = 0  # Số lần dự đoán đúng 4/6 số trượt
        bad_predictions = 0  # Số lần dự đoán đúng ≤3/6 số trượt

        # Thống kê vé đánh
        total_winning_tickets = 0  # Tổng số vé thắng trong ngày
        total_tickets_played = 0   # Tổng số vé đánh trong ngày

        # Load dữ liệu lịch sử cho predictor
        if not self.load_recent_data(100):
            print("Không thể load dữ liệu lịch sử")
            return None

        # Test từ kì 51 đến cuối ngày (21:44:00)
        for draw_index in range(50, len(day_draws)):  # Từ kì 51 (index 50)
            draw_number = draw_index + 1

            # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
            input_draws = []
            for i in range(draw_index):
                input_draws.append(day_draws[i]['results'])

            # Dự đoán 6 số trượt bằng ensemble prediction
            try:
                predicted_missing = self.ensemble_prediction(
                    day_results=input_draws,
                    num_predictions=6,
                    current_draw_index=draw_index  # Truyền vị trí kì hiện tại
                )

                if predicted_missing:
                    # Chờ 5s để lấy kết quả từ DB
                    time.sleep(0.001)

                    # Lấy kết quả thực tế
                    actual_results = day_draws[draw_index]['results']
                    actual_time = day_draws[draw_index]['time']

                    # Tính độ chính xác
                    _, correct, _, _, _ = self.calculate_accuracy(predicted_missing, actual_results)

                    # Tính kết quả vé đánh
                    winning_tickets, total_tickets, win_rate = self.calculate_ticket_results(predicted_missing, actual_results)

                    predictions_count += 1
                    total_winning_tickets += winning_tickets
                    total_tickets_played += total_tickets

                    # Đếm các loại dự đoán
                    if correct >= 6:
                        excellent_predictions += 1
                        perfect_predictions += 1
                    elif correct >= 5:
                        good_predictions += 1
                        perfect_predictions += 1
                    elif correct >= 4:
                        normal_predictions += 1
                    else:
                        bad_predictions += 1

                    # Lưu dữ liệu có nhãn (chỉ khi enable_short_term=True)
                    if self.enable_short_term:
                        draw_info = {
                            'date': str(test_date),  # Convert date to string
                            'draw_number': draw_number,
                            'time': str(actual_time)  # Convert time to string
                        }
                        self.save_labeled_data_entry(input_draws, predicted_missing, actual_results, correct, draw_info)

                    # Lấy thông tin số bị loại bỏ từ tracker (chỉ hiển thị nếu enable_exclusion=True)
                    excluded_info = ""
                    if self.enable_exclusion:
                        excluded_numbers = set(self.excluded_numbers_tracker.keys())
                        if excluded_numbers:
                            excluded_info = f"  Số loại bỏ:    {sorted(list(excluded_numbers))} (xuất hiện >3 lần/5 kì)"
                        else:
                            excluded_info = "  Số loại bỏ:    [] (không có)"

                    # Hiển thị chi tiết - tập trung vào kết quả 5/6 và vé đánh
                    label = self.create_prediction_label(correct)
                    status_icon = "⭐" if correct >= 5 else "❌" if correct <= 3 else "⚠️"

                    print(f"Kì {draw_number:3d} ({actual_time}): {correct}/6 đúng - {label} {status_icon} | Vé: {winning_tickets}/15 thắng ({win_rate:.1f}%)")
                    if excluded_info:
                        print(f"  {excluded_info}")
                    print()

            except Exception as e:
                pass

        # Tính độ chính xác cho cả ngày - hiển thị đầy đủ các tỷ lệ
        if predictions_count > 0:
            perfect_rate = (perfect_predictions / predictions_count) * 100
            excellent_rate = (excellent_predictions / predictions_count) * 100
            good_rate = (good_predictions / predictions_count) * 100
            normal_rate = (normal_predictions / predictions_count) * 100
            bad_rate = (bad_predictions / predictions_count) * 100

            # Tính tỷ lệ thắng vé tổng thể
            overall_ticket_win_rate = (total_winning_tickets / total_tickets_played) * 100 if total_tickets_played > 0 else 0

            print(f"📊 {test_date}: ≥5/6={perfect_rate:.1f}% | Vé={overall_ticket_win_rate:.1f}% | Kì={predictions_count}")



            result = {
                'date': str(test_date),  # Convert to string for JSON
                'predictions_count': predictions_count,
                'perfect_predictions': perfect_predictions,
                'perfect_rate': perfect_rate,
                'excellent_predictions': excellent_predictions,
                'excellent_rate': excellent_rate,
                'good_predictions': good_predictions,
                'good_rate': good_rate,
                'normal_predictions': normal_predictions,
                'normal_rate': normal_rate,
                'bad_predictions': bad_predictions,
                'bad_rate': bad_rate,
                'total_winning_tickets': total_winning_tickets,
                'total_tickets_played': total_tickets_played,
                'overall_ticket_win_rate': overall_ticket_win_rate
            }

            # Lưu kết quả vào daily_results để thống kê
            self.daily_results.append(result)

            return result

        return None

    def print_daily_summary_logs(self, total_perfect_predictions, total_prediction_count):
        """In thống kê logs các ngày - hiển thị đầy đủ tỷ lệ và thông tin vé đánh"""
        print(f"\n" + "="*90)
        print("📊 THỐNG KÊ CHI TIẾT TỶ LỆ DỰ ĐOÁN CÁC NGÀY")
        print("="*90)

        if not self.daily_results:
            print("Không có dữ liệu ngày để thống kê")
            return

        # Sắp xếp theo ngày
        sorted_results = sorted(self.daily_results, key=lambda x: x['date'])

        # Header bảng
        print(f"{'Ngày':<12} {'6/6':<8} {'5/6':<8} {'≥5/6':<8} {'4/6':<8} {'≤3/6':<8} {'Kì':<5} {'Vé thắng':<12}")
        print("-" * 85)

        # Tính tổng
        total_excellent = sum(r['excellent_predictions'] for r in sorted_results)
        total_good = sum(r['good_predictions'] for r in sorted_results)
        total_normal = sum(r['normal_predictions'] for r in sorted_results)
        total_bad = sum(r['bad_predictions'] for r in sorted_results)
        total_winning_tickets_all = sum(r.get('total_winning_tickets', 0) for r in sorted_results)
        total_tickets_played_all = sum(r.get('total_tickets_played', 0) for r in sorted_results)

        # In từng ngày
        for result in sorted_results:
            date_str = str(result['date'])
            excellent_str = f"{result['excellent_predictions']}({result['excellent_rate']:.0f}%)"
            good_str = f"{result['good_predictions']}({result['good_rate']:.0f}%)"
            perfect_str = f"{result['perfect_predictions']}({result['perfect_rate']:.0f}%)"
            normal_str = f"{result['normal_predictions']}({result['normal_rate']:.0f}%)"
            bad_str = f"{result['bad_predictions']}({result['bad_rate']:.0f}%)"
            predictions_str = f"{result['predictions_count']}"

            # Thông tin vé đánh
            winning_tickets = result.get('total_winning_tickets', 0)
            total_tickets = result.get('total_tickets_played', 0)
            ticket_rate = result.get('overall_ticket_win_rate', 0)
            ticket_str = f"{winning_tickets}/{total_tickets}({ticket_rate:.0f}%)"

            print(f"{date_str:<12} {excellent_str:<8} {good_str:<8} {perfect_str:<8} {normal_str:<8} {bad_str:<8} {predictions_str:<5} {ticket_str:<12}")

        # Thống kê tổng hợp
        print("-" * 85)
        overall_perfect_rate = (total_perfect_predictions / total_prediction_count) * 100 if total_prediction_count > 0 else 0
        overall_excellent_rate = (total_excellent / total_prediction_count) * 100 if total_prediction_count > 0 else 0
        overall_good_rate = (total_good / total_prediction_count) * 100 if total_prediction_count > 0 else 0
        overall_normal_rate = (total_normal / total_prediction_count) * 100 if total_prediction_count > 0 else 0
        overall_bad_rate = (total_bad / total_prediction_count) * 100 if total_prediction_count > 0 else 0
        overall_ticket_win_rate_all = (total_winning_tickets_all / total_tickets_played_all) * 100 if total_tickets_played_all > 0 else 0

        excellent_total_str = f"{total_excellent}({overall_excellent_rate:.0f}%)"
        good_total_str = f"{total_good}({overall_good_rate:.0f}%)"
        perfect_total_str = f"{total_perfect_predictions}({overall_perfect_rate:.0f}%)"
        normal_total_str = f"{total_normal}({overall_normal_rate:.0f}%)"
        bad_total_str = f"{total_bad}({overall_bad_rate:.0f}%)"
        ticket_total_str = f"{total_winning_tickets_all}/{total_tickets_played_all}({overall_ticket_win_rate_all:.0f}%)"

        print(f"{'TỔNG':<12} {excellent_total_str:<8} {good_total_str:<8} {perfect_total_str:<8} {normal_total_str:<8} {bad_total_str:<8} {total_prediction_count:<5} {ticket_total_str:<12}")

        # Phân tích chi tiết
        print(f"\n� PHÂN TÍCH CHI TIẾT:")
        print(f"   • 6/6 đúng (Excellent): {total_excellent}/{total_prediction_count} lần ({overall_excellent_rate:.1f}%)")
        print(f"   • 5/6 đúng (Good):      {total_good}/{total_prediction_count} lần ({overall_good_rate:.1f}%)")
        print(f"   • ≥5/6 đúng (Perfect):  {total_perfect_predictions}/{total_prediction_count} lần ({overall_perfect_rate:.1f}%)")
        print(f"   • 4/6 đúng (Normal):    {total_normal}/{total_prediction_count} lần ({overall_normal_rate:.1f}%)")
        print(f"   • ≤3/6 đúng (Bad):      {total_bad}/{total_prediction_count} lần ({overall_bad_rate:.1f}%)")
        print(f"   🎫 VÉ ĐÁNH TỔNG THỂ:   {total_winning_tickets_all}/{total_tickets_played_all} vé thắng ({overall_ticket_win_rate_all:.1f}%)")

        # Xu hướng
        if len(sorted_results) >= 3:
            recent_3 = sorted_results[-3:]
            recent_perfect = sum(r['perfect_rate'] for r in recent_3) / len(recent_3)
            recent_excellent = sum(r['excellent_rate'] for r in recent_3) / len(recent_3)
            recent_good = sum(r['good_rate'] for r in recent_3) / len(recent_3)

            print(f"\n📊 XU HƯỚNG 3 NGÀY GẦN NHẤT:")
            print(f"   • 6/6 đúng: {recent_excellent:.1f}%")
            print(f"   • 5/6 đúng: {recent_good:.1f}%")
            print(f"   • ≥5/6 đúng: {recent_perfect:.1f}%")

        # Ngày tốt nhất
        best_perfect = max(sorted_results, key=lambda x: x['perfect_rate'])
        best_excellent = max(sorted_results, key=lambda x: x['excellent_rate'])

        print(f"\n🏆 NGÀY TỐT NHẤT:")
        print(f"   • ≥5/6: {best_perfect['date']} - {best_perfect['perfect_rate']:.1f}%")
        print(f"   • 6/6:  {best_excellent['date']} - {best_excellent['excellent_rate']:.1f}%")

        # In % trung bình từng ngày để tiện theo dõi
        print(f"\n📈 TỶ LỆ TRUNG BÌNH TỪNG NGÀY:")
        for result in sorted_results:
            date_str = str(result['date'])
            perfect_rate = result['perfect_rate']
            ticket_rate = result.get('overall_ticket_win_rate', 0)
            print(f"   {date_str}: ≥5/6={perfect_rate:.1f}% | Vé={ticket_rate:.1f}%")

        print("="*90)

    def test_all_days(self):
        """Test tất cả ngày từ 2025-04-01"""
        print("=== Test độ chính xác dự đoán (từ 2025-04-01) ===")
        print("Sử dụng: LSTM Model + Phân tích ngắn hạn 10 kì + Vector đặc trưng + Loại bỏ số nóng")
        print("Tính năng: Loại bỏ số xuất hiện quá 3 lần trong 5 kì gần nhất khỏi dự đoán")

        # Clear kết quả cũ để bắt đầu test mới
        self.daily_results = []

        test_dates = self.get_test_dates()

        if not test_dates:
            print("Không có ngày nào để test")
            return

        print(f"Sẽ test {len(test_dates)} ngày")

        total_correct = 0
        total_predictions = 0
        total_perfect_predictions = 0
        total_prediction_count = 0
        successful_days = 0

        for i, test_date in enumerate(test_dates, 1):
            print(f"\n[{i}/{len(test_dates)}]", end=" ")

            day_result = self.test_single_day(test_date)

            print(day_result)
            if day_result:
                total_perfect_predictions += day_result['perfect_predictions']
                total_prediction_count += day_result['predictions_count']
                successful_days += 1

        # Tổng kết
        if total_predictions > 0:
            overall_accuracy = (total_correct / total_predictions) * 100
            overall_perfect_rate = (total_perfect_predictions / total_prediction_count) * 100 if total_prediction_count > 0 else 0

            print(f"\n=== TỔNG KẾT ===")
            print(f"Tổng thể: {total_correct}/{total_predictions} đúng ({overall_accuracy:.2f}%)")
            print(f"Dự đoán xuất sắc (≥5/6): {total_perfect_predictions}/{total_prediction_count} lần ({overall_perfect_rate:.2f}%)")
            print(f"Số ngày test: {successful_days}/{len(test_dates)}")
            print(f"Phương pháp: LSTM (60%) + Xu hướng ngắn hạn 10 kì (40%) + Loại bỏ số nóng")

            # Thống kê logs các ngày để tiện theo dõi
            self.print_daily_summary_logs(total_perfect_predictions, total_prediction_count)
        else:
            print("\nKhông có kết quả test nào")

def predict_6_missing_numbers(enable_short_term=False, enable_exclusion=False, enable_ensemble=False):
    """Dự đoán nhanh 6 số trượt với các tùy chọn tính năng"""
    predictor = EnhancedKenoPredictor(
        enable_short_term=enable_short_term,
        enable_exclusion=enable_exclusion,
        enable_ensemble=enable_ensemble
    )
    predictions = predictor.predict_missing_numbers(num_predictions=6)

    if predictions:
        return predictions
    else:
        return []

def test_with_data_from_2025_04_01(enable_short_term=True, enable_exclusion=True, enable_ensemble=True):
    """Test với dữ liệu từ 2025-04-01, dự đoán từ kì 51"""
    predictor = EnhancedKenoPredictor(
        enable_short_term=enable_short_term,
        enable_exclusion=enable_exclusion,
        enable_ensemble=enable_ensemble
    )
    predictor.test_all_days()

def create_labeled_dataset():
    """Tạo dataset có nhãn để train model"""
    print("🔄 Tạo dataset có nhãn với tính năng ngắn hạn...")
    predictor = EnhancedKenoPredictor(
        enable_short_term=True,  # Bắt buộc để tạo labeled data
        enable_exclusion=True,
        enable_ensemble=True
    )

    # Clear dữ liệu cũ
    predictor.labeled_data = []

    # Test tất cả ngày để tạo labeled data
    predictor.test_all_days()

    # Lưu tất cả dữ liệu vào một file tổng hợp
    if predictor.labeled_data:
        predictor.save_labeled_data_to_file("keno_labeled_dataset_full.json")
        print(f"\n✅ Đã tạo dataset với {len(predictor.labeled_data)} entries")
    else:
        print("\n❌ Không có dữ liệu để tạo dataset")

def test_single_day_detailed(test_date=None, enable_short_term=True, enable_exclusion=True, enable_ensemble=True):
    """Test chi tiết một ngày cụ thể"""
    predictor = EnhancedKenoPredictor(
        enable_short_term=enable_short_term,
        enable_exclusion=enable_exclusion,
        enable_ensemble=enable_ensemble
    )

    if test_date is None:
        # Lấy ngày gần nhất >= 2025-04-01
        test_dates = predictor.get_test_dates()
        if test_dates:
            test_date = test_dates[-1]  # Ngày gần nhất
        else:
            print("Không có ngày nào để test")
            return

    print(f"=== Test chi tiết ngày {test_date} ===")

    # Hiển thị tính năng được bật
    features = []
    if enable_ensemble:
        if enable_short_term:
            features.append("Phân tích ngắn hạn 10 kì")
        if enable_exclusion:
            features.append("Loại bỏ số nóng")
        features_str = " + ".join(features) if features else "Không có"
        print(f"Phương pháp: LSTM Model + {features_str}")
    else:
        print("Phương pháp: LSTM Model only")

    if enable_exclusion:
        print("Tính năng: Loại bỏ số xuất hiện quá 3 lần trong 5 kì gần nhất khỏi dự đoán")

    result = predictor.test_single_day(test_date)

    if result:
        print(f"\n=== TỔNG KẾT NGÀY {test_date} ===")
        print(f"Dự đoán xuất sắc (≥5/6): {result['perfect_predictions']}/{result['predictions_count']} lần ({result['perfect_rate']:.2f}%)")
        print(f"Số kì test: {result['predictions_count']} kì (từ kì 51 đến cuối ngày)")

    return result

def main():
    """Main function"""
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'test':
            # Test với dữ liệu từ 2025-04-01 (tất cả ngày) - mặc định bật tất cả tính năng
            test_with_data_from_2025_04_01()
        elif mode == 'testlstm':
            # Test chỉ với LSTM model
            test_with_data_from_2025_04_01(enable_short_term=False, enable_exclusion=False, enable_ensemble=False)
        elif mode == 'testday':
            # Test chi tiết một ngày
            test_date = sys.argv[2] if len(sys.argv) > 2 else None
            test_single_day_detailed(test_date)
        elif mode == 'predict':
            # Dự đoán nhanh - mặc định chỉ LSTM
            predictions = predict_6_missing_numbers()
            if predictions:
                print(f"6 số dự đoán trượt (LSTM only): {predictions}")
            else:
                print("Không thể dự đoán")
        elif mode == 'predictfull':
            # Dự đoán với tất cả tính năng
            predictions = predict_6_missing_numbers(enable_short_term=True, enable_exclusion=True, enable_ensemble=True)
            if predictions:
                print(f"6 số dự đoán trượt (Full features): {predictions}")
            else:
                print("Không thể dự đoán")
        elif mode == 'dataset':
            # Tạo labeled dataset
            create_labeled_dataset()
        else:
            print("Sử dụng:")
            print("  python simple_keno_predictor.py predict           # Dự đoán 6 số trượt (LSTM only)")
            print("  python simple_keno_predictor.py predictfull       # Dự đoán với tất cả tính năng")
            print("  python simple_keno_predictor.py test              # Test tất cả ngày (full features)")
            print("  python simple_keno_predictor.py testlstm          # Test chỉ LSTM model")
            print("  python simple_keno_predictor.py testday           # Test chi tiết ngày gần nhất")
            print("  python simple_keno_predictor.py testday 2025-04-01 # Test chi tiết ngày cụ thể")
            print("  python simple_keno_predictor.py dataset           # Tạo labeled dataset")
            print()
            print("Tính năng flags:")
            print("  - enable_short_term: Phân tích ngắn hạn 10 kì")
            print("  - enable_exclusion: Loại bỏ số nóng (>3 lần/5 kì)")
            print("  - enable_ensemble: Kết hợp LSTM + tính năng bổ sung")
            print()
            print("Nhãn dataset:")
            print("  - Very Good: 6/6 đúng")
            print("  - Good: 5/6 đúng")
            print("  - Normal: 4/6 đúng")
            print("  - Bad: ≤3/6 đúng")
    else:
        # Mặc định dự đoán chỉ LSTM
        predictions = predict_6_missing_numbers()
        if predictions:
            print(f"6 số dự đoán trượt (LSTM only): {predictions}")
        else:
            print("Không thể dự đoán")

if __name__ == "__main__":
    main()
